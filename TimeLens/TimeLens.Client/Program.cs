using System.Net.Http.Headers;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Http;
using TimeLens.Client.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Add Ant Design
builder.Services.AddAntDesign();

// Add HttpClient
builder.Services.AddScoped(sp => new HttpClient
{
    DefaultRequestHeaders =
    {
        UserAgent =
        {
            new ProductInfoHeaderValue("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        }
    }
});

// Add Services
builder.Services.AddScoped<NewsService>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<HttpContextAccessor>();

// Set default culture
var culture = new System.Globalization.CultureInfo("zh-CN");
System.Globalization.CultureInfo.DefaultThreadCurrentCulture = culture;
System.Globalization.CultureInfo.DefaultThreadCurrentUICulture = culture;

// Configure Ant Design
// Theme configuration removed as DocTheme is not available in this version

await builder.Build().RunAsync();
