using System;
using System.Collections.Generic;

namespace TimeLens.Client.Models
{
    public class NewsItem
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; }
        public string Content { get; set; }
        public DateTime PublishDate { get; set; }
        public string Company { get; set; }
        public List<string> Tags { get; set; } = new();
        public NewsCategory Category { get; set; }
        public string Summary { get; set; }
        public string Source { get; set; }
    }

    public enum NewsCategory
    {
        Restructuring,  // 重组
        Dividend,       // 分红
        AdditionalIssue, // 增发
        AnnualReport,   // 年报
        QuarterlyReport, // 季报
        ShareholdersMeeting, // 股东大会
        InvestorRelations,   // 投资者关系
        Other          // 其他
    }

    public class CompanyInfo
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string StockCode { get; set; }
        public List<ShareholderChange> ShareholderChanges { get; set; } = new();
        public List<MajorShareholder> MajorShareholders { get; set; } = new();
        public List<string> Concepts { get; set; } = new();
        public decimal PriceChange3M { get; set; } // 3个月涨跌幅
    }

    public class ShareholderChange
    {
        public DateTime Date { get; set; }
        public int ShareholderCount { get; set; }
        public decimal ChangeAmount { get; set; } // 增减数量
        public decimal ChangePercent { get; set; } // 增减比例
    }

    public class MajorShareholder
    {
        public string Name { get; set; }
        public decimal SharePercentage { get; set; }
        public decimal ChangeAmount { get; set; } // 持股变动数量
        public string ChangeType { get; set; } // 增持/减持/新进/不变
    }
}
