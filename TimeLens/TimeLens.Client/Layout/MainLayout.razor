@inherits LayoutComponentBase

<div class="notion-app">
    <div class="notion-sidebar">
        <div class="notion-sidebar-header">
            <div class="notion-logo">财迹</div>
        </div>
        <AntDesign.Menu Mode="MenuMode.Inline" Theme="MenuTheme.Light" Style="width: 240px">
            <AntDesign.MenuItem Key="home" Icon=@IconType.Outline.Home>首页</AntDesign.MenuItem>
            <AntDesign.SubMenu Key="recent" Title="最近" Icon=@IconType.Outline.ClockCircle>
                <AntDesign.MenuItem Key="today">今天</AntDesign.MenuItem>
                <AntDesign.MenuItem Key="yesterday">昨天</AntDesign.MenuItem>
                <AntDesign.MenuItem Key="last7days">最近7天</AntDesign.MenuItem>
            </AntDesign.SubMenu>
        </AntDesign.Menu>
    </div>

    <div class="notion-content">
        <div class="notion-navbar">
            <div class="search-bar">
                <AntDesign.Search @bind-Value="@searchText" Placeholder="搜索公司或关键词..." 
                       Style="width: 300px" 
                       OnChange="HandleSearch">
                    <AntDesign.Suffix>
                        <AntDesign.Icon Type="search" />
                    </AntDesign.Suffix>
                </AntDesign.Search>
            </div>
        </div>
        
        <div class="notion-page-content">
            @Body
        </div>
    </div>
</div>

<div id="blazor-error-ui">
    <div class="error-content">
        <AntDesign.Icon Type="close-circle" Theme="IconThemeType.Outline" />
        <span>发生错误，请</span>
        <a class="reload">刷新页面</a>
    </div>
</div>

@code {
    private string searchText = string.Empty;
    
    private void HandleSearch()
    {
        // Search functionality will be implemented in the list page
    }
}