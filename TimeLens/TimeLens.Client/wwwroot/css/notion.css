/* Notion Style Variables */
:root {
    --notion-black: #191711;
    --notion-gray: #9b9a97;
    --notion-gray-light: #f1f1ef;
    --notion-orange: #e9a16d;
    --notion-blue: #6d8bdc;
    --notion-green: #6bd9a4;
    --notion-pink: #f28b82;
    --notion-purple: #d4a5f3;
    --notion-yellow: #f7d66d;
    --notion-red: #f55c5c;
    --notion-teal: #7aece5;
    --notion-default: #37352f;
}

/* Base Styles */
.notion-app {
    display: flex;
    height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, "Apple Color Emoji", "Segoe UI Emoji", sans-serif;
    color: var(--notion-black);
    background-color: #fff;
}

/* Sidebar Styles */
.notion-sidebar {
    width: 240px;
    height: 100%;
    background-color: #f7f6f3;
    border-right: 1px solid rgba(55, 53, 47, 0.1);
    display: flex;
    flex-direction: column;
}

.notion-sidebar-header {
    padding: 12px 14px;
    font-weight: 700;
    font-size: 16px;
    border-bottom: 1px solid rgba(55, 53, 47, 0.1);
}

.notion-logo {
    font-size: 18px;
    font-weight: 700;
    color: var(--notion-black);
}

/* Content Area */
.notion-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Navbar */
.notion-navbar {
    height: 45px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid rgba(55, 53, 47, 0.1);
}

.search-bar {
    width: 100%;
    max-width: 600px;
}

/* Page Content */
.notion-page-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 40px;
    background-color: #fff;
}

/* News List */
.news-date-group {
    margin-bottom: 32px;
}

.news-date {
    font-size: 14px;
    color: var(--notion-gray);
    margin-bottom: 12px;
    font-weight: 500;
}

.news-item {
    padding: 12px 0;
    border-bottom: 1px solid rgba(55, 53, 47, 0.1);
    cursor: pointer;
    transition: background-color 0.2s;
}

.news-item:hover {
    background-color: var(--notion-gray-light);
}

.news-title {
    font-size: 16px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.news-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

/* Tags */
.tag {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.tag-company {
    background-color: var(--notion-blue);
    color: white;
}

.tag-category {
    background-color: var(--notion-green);
    color: var(--notion-black);
}

/* Error UI */
#blazor-error-ui {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #f55c5c;
    color: white;
    padding: 12px 20px;
    z-index: 1000;
}

.error-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.error-content a {
    color: white;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notion-sidebar {
        position: fixed;
        z-index: 100;
        transform: translateX(-100%);
        transition: transform 0.2s;
    }
    
    .notion-sidebar.open {
        transform: translateX(0);
    }
    
    .notion-content {
        margin-left: 0;
    }
    
    .notion-page-content {
        padding: 15px;
    }
}
