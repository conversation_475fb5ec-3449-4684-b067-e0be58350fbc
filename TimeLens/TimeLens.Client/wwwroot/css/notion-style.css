/* Notion-like styles */
:root {
    --notion-black: #191711;
    --notion-gray: #f7f6f3;
    --notion-gray-light: #f1f1ef;
    --notion-orange: #e9a15a;
    --notion-blue: #9dbdd6;
    --notion-green: #8cc2a0;
    --notion-pink: #e9a3a1;
    --notion-purple: #c4a5e5;
    --notion-yellow: #f0d77b;
    --notion-red: #e07a5f;
    --notion-teal: #64b5a7;
    --notion-default: #e3e2e0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--notion-gray);
    color: var(--notion-black);
    line-height: 1.5;
}

/* Override Ant Design styles */
.ant-layout {
    background: transparent;
}

.ant-layout-header {
    background: white;
    padding: 0 24px;
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.ant-menu {
    border: none !important;
}

.ant-menu-item {
    border-radius: 4px;
    margin: 2px 4px !important;
    width: auto !important;
}

.ant-menu-item-selected {
    background-color: rgba(25, 23, 17, 0.08) !important;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Custom tags */
.ant-tag {
    border-radius: 4px;
    font-size: 12px;
    line-height: 18px;
    padding: 0 6px;
    margin-right: 6px;
    margin-bottom: 4px;
    border: none;
}

/* Card styles */
.ant-card {
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.ant-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Input styles */
.ant-input {
    border-radius: 6px;
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.ant-input:focus, .ant-input-focused {
    box-shadow: 0 0 0 2px rgba(25, 23, 17, 0.1);
}

/* Button styles */
.ant-btn {
    border-radius: 6px;
    font-weight: 500;
}

.ant-btn-primary {
    background-color: var(--notion-black);
    border-color: var(--notion-black);
}

.ant-btn-primary:hover, .ant-btn-primary:focus {
    background-color: #2b2a27;
    border-color: #2b2a27;
}

/* Table styles */
.ant-table {
    border-radius: 8px;
    overflow: hidden;
}

.ant-table-thead > tr > th {
    background: var(--notion-gray-light);
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ant-layout-header {
        padding: 0 16px;
    }
    
    .ant-col {
        padding: 0 8px !important;
    }
}
