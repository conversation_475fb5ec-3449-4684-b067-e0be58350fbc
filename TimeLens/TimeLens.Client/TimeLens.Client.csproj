<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
        <StaticWebAssetProjectMode>Default</StaticWebAssetProjectMode>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AntDesign" Version="1.4.2" />
        <PackageReference Include="AntDesign.Charts" Version="0.7.3" />
        <PackageReference Include="AntDesign.Icons" Version="0.1.1" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.5"/>
        <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    </ItemGroup>

    <ItemGroup>
      <AdditionalFiles Include="Layout\MainLayout.razor" />
      <AdditionalFiles Include="Layout\NavMenu.razor" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Services\" />
    </ItemGroup>

</Project>
