@page "/news"
@inject NavigationManager NavigationManager

<div class="news-container">
    <div class="search-container">
        <Search @bind-Value="_searchQuery" Placeholder="搜索公司或关键词..." Style="width: 100%; max-width: 600px">
            <Suffix>
                <Icon Type="search" />
            </Suffix>
        </Search>
    </div>

    @if (_isLoading)
    {
        <div class="loading-container">
            <Spin Size="SpinSize.Large" />
            <div>加载中...</div>
        </div>
    }
    else if (_newsGroups.Any())
    {
        foreach (var group in _newsGroups)
        {
            <div class="news-date-group">
                <div class="news-date">@group.Date.ToString("yyyy年M月d日 dddd", new CultureInfo("zh-CN"))</div>
                
                @foreach (var news in group.NewsItems)
                {
                    <div class="news-item" @onclick="() => NavigateToDetail(news.Id)">
                        <div class="news-title">@news.Title</div>
                        <div class="news-meta">
                            <span class="tag tag-company">@news.Company</span>
                            <span class="tag tag-category">@news.Category</span>
                            <span class="news-time">@news.PublishTime.ToString("HH:mm")</span>
                        </div>
                    </div>
                }
            </div>
        }
    }
    else
    {
        <Empty >
            <DescriptionTemplate>
                <span>暂无相关新闻</span>
            </DescriptionTemplate>
        </Empty>
    }

    <div style="margin: 20px 0; text-align: center;">
        <Button Type="@ButtonType.Default" Loading="_isLoadingMore" OnClick="LoadMoreNews">
            加载更多
        </Button>
    </div>
</div>

@code {
    private string _searchQuery = string.Empty;
    private bool _isLoading = true;
    private bool _isLoadingMore = false;
    private int _page = 1;
    private const int PageSize = 20;

    private List<NewsGroup> _newsGroups = new();
    
    protected override async Task OnInitializedAsync()
    {
        await LoadNews();
    }

    private Task LoadNews(bool loadMore = false)
    {
        if (!loadMore)
        {
            _isLoading = true;
            _page = 1;
            StateHasChanged();
        }
        else
        {
            _isLoadingMore = true;
        }

        try
        {
            // In a real app, you would fetch data from an API
            // var response = await Http.GetFromJsonAsync<NewsResponse>($"api/news?page={_page}&pageSize={PageSize}&search={_searchQuery}");
            
            // Mock data for demonstration
            var mockNews = GenerateMockNews();
            
            if (loadMore)
            {
                _newsGroups = GroupNewsByDate(_newsGroups.SelectMany(g => g.NewsItems).Concat(mockNews).ToList());
            }
            else
            {
                _newsGroups = GroupNewsByDate(mockNews);
            }
            
            _page++;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading news: {ex.Message}");
            // In a real app, show error message to user
        }
        finally
        {
            _isLoading = false;
            _isLoadingMore = false;
            StateHasChanged();
        }
        return Task.CompletedTask;
    }

    private List<NewsGroup> GroupNewsByDate(List<NewsItem> newsItems)
    {
        return newsItems
            .GroupBy(n => n.PublishTime.Date)
            .OrderByDescending(g => g.Key)
            .Select(g => new NewsGroup
            {
                Date = g.Key,
                NewsItems = g.OrderByDescending(n => n.PublishTime).ToList()
            })
            .ToList();
    }

    private void NavigateToDetail(string newsId)
    {
        NavigationManager.NavigateTo($"/news/{newsId}");
    }

    private async Task LoadMoreNews()
    {
        await LoadNews(true);
    }

    // Mock data generation - replace with actual API calls in production
    private List<NewsItem> GenerateMockNews()
    {
        var companies = new[] { "腾讯控股", "阿里巴巴", "贵州茅台", "宁德时代", "中国平安", "美团", "京东", "小米集团" };
        var categories = new[] { "年报", "季报", "重大资产重组", "分红", "增发", "股东大会", "投资者关系" };
        var titles = new[]
        {
            "2023年度业绩报告",
            "关于公司股份回购的进展公告",
            "关于公司实际控制人增持公司股份的公告",
            "关于召开2023年年度股东大会的通知",
            "关于公司重大资产重组事项的进展公告",
            "2023年第三季度报告",
            "关于公司收到中国证监会行政许可申请受理单的公告",
            "关于公司控股股东部分股份解除质押的公告"
        };

        var newsList = new List<NewsItem>();
        var random = new Random();
        var now = DateTime.Now;

        for (int i = 0; i < 10; i++)
        {
            var daysAgo = random.Next(0, 14);
            var hoursAgo = random.Next(0, 24);
            var minutesAgo = random.Next(0, 60);
            
            var publishTime = now.AddDays(-daysAgo).AddHours(-hoursAgo).AddMinutes(-minutesAgo);
            
            newsList.Add(new NewsItem
            {
                Id = Guid.NewGuid().ToString(),
                Title = titles[random.Next(titles.Length)],
                Company = companies[random.Next(companies.Length)],
                Category = categories[random.Next(categories.Length)],
                PublishTime = publishTime,
                Content = "这里是新闻的详细内容..."
            });
        }

        return newsList;
    }

    public class NewsItem
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public DateTime PublishTime { get; set; }
        public string Content { get; set; } = string.Empty;
    }

    public class NewsGroup
    {
        public DateTime Date { get; set; }
        public List<NewsItem> NewsItems { get; set; } = new();
    }

    public class NewsResponse
    {
        public List<NewsItem> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
    }
}
