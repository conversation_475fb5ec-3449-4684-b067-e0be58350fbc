@page "/news/{Id}"
@using TimeLens.Client.Shared
@inject NewsService NewsService
@inject NavigationManager NavigationManager


<div class="news-detail">
    <div class="news-header">
        <AntDesign.Button Icon="arrow-left" Type="ButtonType.Text" OnClick="@(() => NavigationManager.NavigateTo("/"))">
            返回列表
        </AntDesign.Button>
        <h1>@newsItem.Title</h1>
        <div class="news-meta">
            <span>@newsItem.Company</span>
            <span>•</span>
            <span>@newsItem.PublishDate.ToString("yyyy-MM-dd HH:mm")</span>
        </div>
        <div class="news-tags">
            <AntDesign.Tag Color="TagColor.Blue">@newsItem.Category</AntDesign.Tag>
            @foreach (var tag in newsItem.Tags)
            {
                <Tag Color="TagColor.Green">@tag</Tag>
            }
        </div>
    </div>

    <div class="news-content">
        @((MarkupString)newsItem.Content)
    </div>

    
    <div class="company-info">
        <h2>@companyInfo.Name (@companyInfo.StockCode) 相关信息</h2>
                
        <Tabs Type="TabType.Card">
            <TabPane Key="shareholders" Tab="股东人数变化">
                <Table TData="ShareholderChange" DataSource="@companyInfo.ShareholderChanges" Bordered="true" Size="TableSize.Small">
                    <Column Title="日期" DataIndex="Date" TData="object" Formatter="(date) => ((DateTime)date).ToString("yyyy-MM-dd")" />
                    <Column Title="股东人数" DataIndex="ShareholderCount" TData="int"/>
                    <Column Title="较上期变化" DataIndex="ChangeAmount" TData="decimal"/>
                    <Column Title="变化比例" DataIndex="ChangePercent" TData="decimal" Formatter="(pct) => $"{(decimal)pct}%"/>
                </Table>
            </TabPane>
                    
            <TabPane Key="majorHolders" Tab="前十大股东">
                <Table TData="MajorShareholder" DataSource="@companyInfo.MajorShareholders" Bordered="true" Size="TableSize.Small">
                    <Column Title="股东名称" DataIndex="Name" TData="string"/>
                    <Column Title="持股比例" DataIndex="SharePercentage" TData="decimal" Formatter="(pct) => $"{(decimal)pct}%"/>
                    <Column Title="持股变动" DataIndex="ChangeAmount" TData="decimal" />
                    <Column Title="变动类型" DataIndex="ChangeType" TData="string"/>
                </Table>
            </TabPane>
                    
            <TabPane Key="concepts" Tab="概念题材">
                <div class="concepts">
                    @foreach (var concept in companyInfo.Concepts)
                    {
                        <Tag Color="TagColor.Purple">@concept</Tag>
                    }
                </div>
                <div class="price-change">
                    <span>近3个月涨跌幅: </span>
                    <span class="@(companyInfo.PriceChange3M >= 0 ? "positive" : "negative")">
                        @(companyInfo.PriceChange3M >= 0 ? "+" : "")@companyInfo.PriceChange3M%
                    </span>
                </div>
            </TabPane>
        </Tabs>
    </div>

    @if (relatedNews.Any())
    {
        <div class="related-news">
            <h2>相关报道</h2>
            @foreach (var item in relatedNews)
            {
                <NewsListItem NewsItem="item" OnItemClick="OnRelatedNewsClick" />
            }
        </div>
    }
</div>

@code {
    [Parameter]
    public string Id { get; set; }

    private NewsItem newsItem;
    private CompanyInfo companyInfo;
    private List<NewsItem> relatedNews = new();

    protected override async Task OnInitializedAsync()
    {
        newsItem = await NewsService.GetNewsItemByIdAsync(Id);

        companyInfo = await NewsService.GetCompanyInfoAsync(newsItem.Company);
        relatedNews = await NewsService.GetRelatedNewsAsync(newsItem.Company, Id);
    }

    private void OnRelatedNewsClick(NewsItem item)
    {
        NavigationManager.NavigateTo($"/news/{item.Id}");
    }
}

<style>
.news-detail {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.news-header {
    margin-bottom: 24px;
}

.news-header h1 {
    font-size: 28px;
    margin: 16px 0 8px 0;
    color: #1a1a1a;
}

.news-meta {
    display: flex;
    gap: 12px;
    color: #666;
    margin-bottom: 12px;
    font-size: 14px;
}

.news-tags {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.news-content {
    line-height: 1.8;
    font-size: 16px;
    color: #333;
    margin-bottom: 40px;
}

.company-info, .related-news {
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
}

.company-info h2, .related-news h2 {
    font-size: 20px;
    margin-bottom: 16px;
    color: #1a1a1a;
}

.concepts {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 16px;
}

.price-change {
    font-size: 16px;
    font-weight: 500;
}

.positive {
    color: #f5222d;
}

.negative {
    color: #52c41a;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 16px;
    color: #666;
}
</style>
