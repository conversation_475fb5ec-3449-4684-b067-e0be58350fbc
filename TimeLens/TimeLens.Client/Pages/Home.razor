@page "/"
@using TimeLens.Client.Shared
@inject NewsService NewsService
@inject NavigationManager NavigationManager

<PageTitle>财迹 - 财经资讯</PageTitle>

<div class="home-container">
    <div class="search-container">
        <div class="search-box">
            <Search @bind-Value="searchText" Placeholder="搜索公司或关键词..."
                   Style="width: 100%; max-width: 600px;"
                   OnSearch="Search">
                <Suffix>
                    <Icon Type="search" />
                </Suffix>
            </Search>
        </div>
    </div>

    <div class="content-container">
        @if (isLoading)
        {
            <div class="loading">
                <Spin Size="SpinSize.Large" />
                <div>加载中...</div>
            </div>
        }
        else if (newsItems?.Any() == true)
        {
            <div class="news-list">
                @foreach (var group in newsItems.GroupBy(n => n.PublishDate.Date).OrderByDescending(g => g.<PERSON>))
                {
                    <div class="news-date-group">
                        <div class="date-header">
                            @GetDateHeader(group.Key)
                        </div>
                        <div class="news-items">
                            @foreach (var item in group.OrderByDescending(n => n.PublishDate))
                            {
                                <NewsListItem NewsItem="item" OnItemClick="OnNewsItemClick" />
                            }
                        </div>
                    </div>
                }
            </div>

            <div class="pagination">
                <Pagination Total="totalItems" PageSize="pageSize" @bind-Current="currentPage" 
                           ShowSizeChanger="false" OnChange="HandlePageChange" />
            </div>
        }
        else
        {
            <div class="empty-state">
                <Icon Type="inbox" Style="font-size: 48px; color: #bfbfbf; margin-bottom: 16px;" />
                <div>暂无数据</div>
            </div>
        }
    </div>
</div>

@code {
    private List<NewsItem> newsItems = new();
    private bool isLoading = true;
    private string searchText = string.Empty;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalItems = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadNews();
    }

    private async Task LoadNews()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            var items = await NewsService.GetNewsItemsAsync(searchText);
            totalItems = items.Count;
            newsItems = items.Skip((currentPage - 1) * pageSize).Take(pageSize).ToList();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task Search()
    {
        currentPage = 1;
        await LoadNews();
    }

    private async Task HandlePageChange(int page, int pageSize)
    {
        currentPage = page;
        await LoadNews();
    }

    private void OnNewsItemClick(NewsItem item)
    {
        NavigationManager.NavigateTo($"/news/{item.Id}");
    }

    private string GetDateHeader(DateTime date)
    {
        var today = DateTime.Today;
        var yesterday = today.AddDays(-1);
        var thisYear = today.Year;

        if (date == today)
            return "今天";
        if (date == yesterday)
            return "昨天";
        if (date > today.AddDays(-7))
            return $"{date:dddd}";
        if (date.Year == thisYear)
            return date.ToString("M月d日 dddd", new CultureInfo("zh-CN"));
        
        return date.ToString("yyyy年M月d日 dddd", new CultureInfo("zh-CN"));
    }
}

<style>
.home-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 24px 16px;
}

.search-container {
    margin-bottom: 24px;
    display: flex;
    justify-content: center;
}

.search-box {
    width: 100%;
    display: flex;
    justify-content: center;
}

.content-container {
    background: white;
    border-radius: 8px;
    padding: 24px;
    min-height: 400px;
}

.news-date-group {
    margin-bottom: 32px;
}

.date-header {
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin-bottom: 12px;
    padding-left: 8px;
    border-left: 3px solid #1890ff;
}

.news-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.loading, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    color: #666;
}

.pagination {
    margin-top: 24px;
    display: flex;
    justify-content: center;
}
</style>