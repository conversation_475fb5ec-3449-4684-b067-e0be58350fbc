using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TimeLens.Client.Models;

namespace TimeLens.Client.Services
{
    public class NewsService
    {
        private List<NewsItem> _newsItems = new();
        private Dictionary<string, CompanyInfo> _companyInfos = new();

        public NewsService()
        {
            InitializeSampleData();
        }

        private void InitializeSampleData()
        {
            // Sample companies
            var companies = new List<CompanyInfo>
            {
                new() { Id = "1", Name = "腾讯控股", StockCode = "00700.HK", 
                        Concepts = new List<string> { "互联网", "游戏", "金融科技" },
                        PriceChange3M = 15.2m },
                new() { Id = "2", Name = "贵州茅台", StockCode = "600519.SH", 
                        Concepts = new List<string> { "白酒", "消费", "蓝筹" },
                        PriceChange3M = -5.8m },
                new() { Id = "3", Name = "宁德时代", StockCode = "300750.SZ", 
                        Concepts = new List<string> { "新能源", "锂电池", "储能" },
                        PriceChange3M = 22.4m },
            };

            // Sample news items
            var news = new List<NewsItem>
            {
                new()
                {
                    Title = "腾讯控股发布2023年年度报告",
                    Content = "腾讯控股今日发布2023年年度报告，全年营收...",
                    PublishDate = DateTime.Today,
                    Company = "腾讯控股",
                    Tags = new List<string> { "年报", "互联网" },
                    Category = NewsCategory.AnnualReport,
                    Summary = "腾讯2023年全年营收同比增长10%，净利润增长12%"
                },
                new()
                {
                    Title = "贵州茅台公布2023年分红方案",
                    Content = "贵州茅台公告称，拟每10股派发现金红利...",
                    PublishDate = DateTime.Today,
                    Company = "贵州茅台",
                    Tags = new List<string> { "分红", "白酒" },
                    Category = NewsCategory.Dividend,
                    Summary = "贵州茅台拟每10股派发现金红利259.11元"
                },
                new()
                {
                    Title = "宁德时代宣布百亿增发计划",
                    Content = "宁德时代公告称，拟非公开发行股票募集资金...",
                    PublishDate = DateTime.Today.AddDays(-1),
                    Company = "宁德时代",
                    Tags = new List<string> { "增发", "新能源" },
                    Category = NewsCategory.AdditionalIssue,
                    Summary = "宁德时代拟非公开发行股票募资不超100亿元"
                },
                new()
                {
                    Title = "腾讯控股将召开年度股东大会",
                    Content = "腾讯控股公告，将于2023年5月15日召开...",
                    PublishDate = DateTime.Today.AddDays(-2),
                    Company = "腾讯控股",
                    Tags = new List<string> { "股东大会", "互联网" },
                    Category = NewsCategory.ShareholdersMeeting,
                    Summary = "腾讯控股2023年年度股东大会将于5月15日召开"
                }
            };

            // Sample shareholder data
            companies[0].ShareholderChanges = new List<ShareholderChange>
            {
                new() { Date = DateTime.Today.AddMonths(-1), ShareholderCount = 450000, ChangeAmount = 5000, ChangePercent = 1.1m },
                new() { Date = DateTime.Today.AddMonths(-2), ShareholderCount = 445000, ChangeAmount = -8000, ChangePercent = -1.8m },
                new() { Date = DateTime.Today.AddMonths(-3), ShareholderCount = 453000, ChangeAmount = 3000, ChangePercent = 0.7m }
            };

            companies[0].MajorShareholders = new List<MajorShareholder>
            {
                new() { Name = "马化腾", SharePercentage = 7.5m, ChangeAmount = 0.2m, ChangeType = "增持" },
                new() { Name = "Prosus N.V.", SharePercentage = 26.9m, ChangeAmount = -0.5m, ChangeType = "减持" },
                new() { Name = "中国证券金融", SharePercentage = 1.2m, ChangeAmount = 0, ChangeType = "不变" }
            };

            _newsItems = news;
            _companyInfos = companies.ToDictionary(c => c.Name, c => c);
        }

        public Task<List<NewsItem>> GetNewsItemsAsync(string searchTerm = null, string company = null, NewsCategory? category = null)
        {
            var query = _newsItems.AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(n => n.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                       n.Content.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(company))
            {
                query = query.Where(n => n.Company == company);
            }

            if (category.HasValue)
            {
                query = query.Where(n => n.Category == category.Value);
            }

            return Task.FromResult(query.OrderByDescending(n => n.PublishDate).ToList());
        }

        public Task<NewsItem> GetNewsItemByIdAsync(string id)
        {
            return Task.FromResult(_newsItems.FirstOrDefault(n => n.Id == id));
        }

        public Task<CompanyInfo> GetCompanyInfoAsync(string companyName)
        {
            _companyInfos.TryGetValue(companyName, out var companyInfo);
            return Task.FromResult(companyInfo);
        }

        public Task<List<NewsItem>> GetRelatedNewsAsync(string companyName, string excludeNewsId = null)
        {
            var query = _newsItems.Where(n => n.Company == companyName);
            
            if (!string.IsNullOrEmpty(excludeNewsId))
            {
                query = query.Where(n => n.Id != excludeNewsId);
            }

            return Task.FromResult(query.OrderByDescending(n => n.PublishDate).Take(5).ToList());
        }
    }
}
