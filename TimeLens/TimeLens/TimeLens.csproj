<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\TimeLens.Client\TimeLens.Client.csproj"/>
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.5"/>
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="Components\Layout\MainLayout.razor" />
      <_ContentIncludedByDefault Remove="Components\Layout\NavMenu.razor" />
    </ItemGroup>

</Project>
